<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <compositeConfiguration>
          <compositeBuild compositeDefinitionSource="SCRIPT">
            <builds>
              <build path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" name="react-native-gradle-plugin">
                <projects>
                  <project path="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" />
                </projects>
              </build>
            </builds>
          </compositeBuild>
        </compositeConfiguration>
        <option name="testRunner" value="CHOOSE_PER_TEST" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleHome" value="C:\Users\<USER>\Downloads\gradle-8.14.3" />
        <option name="gradleJvm" value="temurin-17 (2)" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native/gradle-plugin" />
            <option value="$PROJECT_DIR$/../../node_modules/@react-native-async-storage/async-storage/android" />
            <option value="$PROJECT_DIR$/../../node_modules/react-native-device-info/android" />
            <option value="$PROJECT_DIR$/../../node_modules/react-native-gesture-handler/android" />
            <option value="$PROJECT_DIR$/../../node_modules/react-native-keychain/android" />
            <option value="$PROJECT_DIR$/../../node_modules/react-native-safe-area-context/android" />
            <option value="$PROJECT_DIR$/../../node_modules/react-native-screens/android" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
    <option name="parallelModelFetch" value="true" />
  </component>
</project>